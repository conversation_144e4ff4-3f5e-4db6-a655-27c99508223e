-- Add admin role and audit logging features for PokéCollector
-- Migration: 20250519000001_add_admin_features.sql

-- Add admin role column to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false;

-- Add admin-related columns for better user management
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Create audit_logs table for tracking admin actions
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL, -- 'user', 'subscription', 'collection', etc.
    entity_id TEXT,
    old_values JSONB,
    new_values JSONB,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Create user_statistics table for admin dashboard
CREATE TABLE IF NOT EXISTS public.user_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    total_cards INTEGER DEFAULT 0,
    total_collections INTEGER DEFAULT 0,
    total_wishlist_items INTEGER DEFAULT 0,
    last_activity_at TIMESTAMPTZ,
    registration_completed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Create subscription_overrides table for admin manual overrides
CREATE TABLE IF NOT EXISTS public.subscription_overrides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    override_type TEXT NOT NULL, -- 'plan_change', 'limit_override', 'status_change'
    original_value TEXT,
    override_value TEXT,
    reason TEXT,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Function to update user statistics
CREATE OR REPLACE FUNCTION update_user_statistics(target_user_id UUID)
RETURNS void AS $$
BEGIN
    INSERT INTO public.user_statistics (
        user_id,
        total_cards,
        total_collections,
        total_wishlist_items,
        last_activity_at,
        updated_at
    )
    SELECT 
        target_user_id,
        COALESCE((
            SELECT COUNT(*) 
            FROM public.collection_cards cc
            JOIN public.collections c ON cc.collection_id = c.id
            WHERE c.user_id = target_user_id
        ), 0),
        COALESCE((
            SELECT COUNT(*) 
            FROM public.collections 
            WHERE user_id = target_user_id
        ), 0),
        COALESCE((
            SELECT COUNT(*) 
            FROM public.wishlist_cards 
            WHERE user_id = target_user_id
        ), 0),
        now(),
        now()
    ON CONFLICT (user_id) 
    DO UPDATE SET
        total_cards = EXCLUDED.total_cards,
        total_collections = EXCLUDED.total_collections,
        total_wishlist_items = EXCLUDED.total_wishlist_items,
        last_activity_at = EXCLUDED.last_activity_at,
        updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
    p_admin_user_id UUID,
    p_target_user_id UUID,
    p_action TEXT,
    p_entity_type TEXT,
    p_entity_id TEXT DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.audit_logs (
        admin_user_id,
        target_user_id,
        action,
        entity_type,
        entity_id,
        old_values,
        new_values,
        metadata,
        ip_address,
        user_agent
    ) VALUES (
        p_admin_user_id,
        p_target_user_id,
        p_action,
        p_entity_type,
        p_entity_id,
        p_old_values,
        p_new_values,
        p_metadata,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update user statistics when collections/cards change
CREATE OR REPLACE FUNCTION trigger_update_user_statistics()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_user_statistics(
            CASE 
                WHEN TG_TABLE_NAME = 'collections' THEN NEW.user_id
                WHEN TG_TABLE_NAME = 'wishlist_cards' THEN NEW.user_id
                WHEN TG_TABLE_NAME = 'collection_cards' THEN (
                    SELECT c.user_id FROM collections c WHERE c.id = NEW.collection_id
                )
            END
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_user_statistics(
            CASE 
                WHEN TG_TABLE_NAME = 'collections' THEN OLD.user_id
                WHEN TG_TABLE_NAME = 'wishlist_cards' THEN OLD.user_id
                WHEN TG_TABLE_NAME = 'collection_cards' THEN (
                    SELECT c.user_id FROM collections c WHERE c.id = OLD.collection_id
                )
            END
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic statistics updates
DROP TRIGGER IF EXISTS trigger_collections_stats ON public.collections;
CREATE TRIGGER trigger_collections_stats
    AFTER INSERT OR DELETE ON public.collections
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

DROP TRIGGER IF EXISTS trigger_collection_cards_stats ON public.collection_cards;
CREATE TRIGGER trigger_collection_cards_stats
    AFTER INSERT OR DELETE ON public.collection_cards
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

DROP TRIGGER IF EXISTS trigger_wishlist_cards_stats ON public.wishlist_cards;
CREATE TRIGGER trigger_wishlist_cards_stats
    AFTER INSERT OR DELETE ON public.wishlist_cards
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

-- Enable RLS on new tables
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_overrides ENABLE ROW LEVEL SECURITY;

-- Policies for audit_logs (admin only)
CREATE POLICY "Admins can view all audit logs"
    ON public.audit_logs
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Policies for user_statistics (users can view own, admins can view all)
CREATE POLICY "Users can view own statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Admins can view all statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Policies for subscription_overrides (admin only)
CREATE POLICY "Admins can manage subscription overrides"
    ON public.subscription_overrides
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Grant permissions
GRANT ALL ON public.audit_logs TO service_role;
GRANT ALL ON public.user_statistics TO service_role;
GRANT ALL ON public.subscription_overrides TO service_role;

GRANT SELECT ON public.audit_logs TO authenticated;
GRANT SELECT, UPDATE ON public.user_statistics TO authenticated;
GRANT SELECT ON public.subscription_overrides TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION update_user_statistics(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION log_admin_action(UUID, UUID, TEXT, TEXT, TEXT, JSONB, JSONB, JSONB, INET, TEXT) TO authenticated;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_admin_user_id ON public.audit_logs(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_user_id ON public.audit_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity_type ON public.audit_logs(entity_type);

CREATE INDEX IF NOT EXISTS idx_users_is_admin ON public.users(is_admin) WHERE is_admin = true;
CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON public.users(last_login_at DESC);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);

CREATE INDEX IF NOT EXISTS idx_user_statistics_user_id ON public.user_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_statistics_last_activity ON public.user_statistics(last_activity_at DESC);

CREATE INDEX IF NOT EXISTS idx_subscription_overrides_user_id ON public.subscription_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_is_active ON public.subscription_overrides(is_active) WHERE is_active = true;

-- Initialize statistics for existing users
INSERT INTO public.user_statistics (user_id)
SELECT id FROM public.users
ON CONFLICT (user_id) DO NOTHING;

-- Update statistics for all existing users
SELECT update_user_statistics(id) FROM public.users;

COMMENT ON TABLE public.audit_logs IS 'Logs all administrative actions for audit purposes';
COMMENT ON TABLE public.user_statistics IS 'Cached statistics for users to improve admin dashboard performance';
COMMENT ON TABLE public.subscription_overrides IS 'Admin overrides for subscription limits and features';
